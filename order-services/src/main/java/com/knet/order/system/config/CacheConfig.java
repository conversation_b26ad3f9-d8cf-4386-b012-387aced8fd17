package com.knet.order.system.config;

import org.springframework.cache.CacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

import static com.knet.common.constants.OrderServicesConstants.ORDER_LIST_CACHE_NAME;

/**
 * <AUTHOR>
 * @date 2025/12/19 15:25
 * @description: 缓存配置类，专门配置不同缓存的过期时间和序列化方式
 */
@Configuration
public class CacheConfig {

    /**
     * 配置CacheManager，用于@Cacheable等注解
     * 支持不同缓存名称的不同配置
     */
    @Bean
    public CacheManager cacheManager(LettuceConnectionFactory lettuceConnectionFactory) {
        // 默认缓存配置
        RedisCacheConfiguration defaultConfig = RedisCacheConfiguration.defaultCacheConfig()
                // 设置缓存的默认过期时间为30分钟
                .entryTtl(Duration.ofMinutes(30))
                // 设置 key为string序列化
                .serializeKeysWith(RedisSerializationContext.SerializationPair.fromSerializer(new StringRedisSerializer()))
                // 设置value为json序列化
                .serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(new GenericJackson2JsonRedisSerializer()))
                // 不缓存空值
                .disableCachingNullValues()
                // 设置缓存key前缀
                .prefixCacheNameWith("order-service:");

        // 针对不同缓存名称的特定配置
        Map<String, RedisCacheConfiguration> cacheConfigurations = new HashMap<>();
        // 订单列表缓存配置 - 30分钟过期
        cacheConfigurations.put(ORDER_LIST_CACHE_NAME, defaultConfig
                .entryTtl(Duration.ofMinutes(30))
                .prefixCacheNameWith("order-service:orderList:"));

        return RedisCacheManager.builder(lettuceConnectionFactory)
                .cacheDefaults(defaultConfig)
                .withInitialCacheConfigurations(cacheConfigurations)
                .build();
    }
}
