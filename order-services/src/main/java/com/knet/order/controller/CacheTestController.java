package com.knet.order.controller;

import com.knet.common.base.HttpResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/12/19 15:30
 * @description: 缓存测试控制器，用于验证缓存配置是否正确
 */
@Slf4j
@RestController
@RequestMapping("/cache")
@Tag(name = "缓存测试控制器", description = "用于测试和验证缓存功能")
public class CacheTestController {

    @Resource
    private CacheManager cacheManager;

    /**
     * 测试缓存配置
     */
    @Operation(summary = "测试缓存配置", description = "检查缓存管理器和缓存配置是否正常")
    @GetMapping("/test")
    public HttpResult<Map<String, Object>> testCache() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 检查CacheManager是否正常
            result.put("cacheManagerClass", cacheManager.getClass().getSimpleName());
            result.put("cacheManagerAvailable", true);
            
            // 检查orderList缓存是否可用
            Cache orderListCache = cacheManager.getCache("orderList");
            result.put("orderListCacheAvailable", orderListCache != null);
            
            if (orderListCache != null) {
                result.put("orderListCacheClass", orderListCache.getClass().getSimpleName());
                
                // 测试缓存写入和读取
                String testKey = "test:cache:key";
                String testValue = "test cache value";
                orderListCache.put(testKey, testValue);
                
                Cache.ValueWrapper valueWrapper = orderListCache.get(testKey);
                if (valueWrapper != null) {
                    result.put("cacheReadWriteTest", "SUCCESS");
                    result.put("cachedValue", valueWrapper.get());
                } else {
                    result.put("cacheReadWriteTest", "FAILED - No value returned");
                }
                
                // 清理测试数据
                orderListCache.evict(testKey);
            }
            
            log.info("缓存测试完成: {}", result);
            return HttpResult.ok(result);
            
        } catch (Exception e) {
            log.error("缓存测试失败: {}", e.getMessage(), e);
            result.put("error", e.getMessage());
            result.put("cacheManagerAvailable", false);
            return HttpResult.fail("缓存测试失败: " + e.getMessage(), result);
        }
    }

    /**
     * 清除所有缓存
     */
    @Operation(summary = "清除所有缓存", description = "清除orderList缓存中的所有数据")
    @GetMapping("/clear")
    public HttpResult<String> clearCache() {
        try {
            Cache orderListCache = cacheManager.getCache("orderList");
            if (orderListCache != null) {
                orderListCache.clear();
                log.info("已清除orderList缓存");
                return HttpResult.ok("缓存清除成功");
            } else {
                return HttpResult.fail("orderList缓存不存在");
            }
        } catch (Exception e) {
            log.error("清除缓存失败: {}", e.getMessage(), e);
            return HttpResult.fail("清除缓存失败: " + e.getMessage());
        }
    }
}
