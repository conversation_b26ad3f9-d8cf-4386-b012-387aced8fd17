package com.knet.order.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.knet.common.annotation.DistributedLock;
import com.knet.common.annotation.Loggable;
import com.knet.common.base.HttpResult;
import com.knet.order.model.dto.req.CreateOrderRequest;
import com.knet.order.model.dto.req.OrderListQueryRequest;
import com.knet.order.model.dto.rsp.CreateOrderResponse;
import com.knet.order.model.dto.rsp.OrderListResponse;
import com.knet.order.service.ISysOrderProcessService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/6/3 15:58
 * @description: 订单服务-订单控制器
 */
@Slf4j
@RestController
@RequestMapping("/order")
@Tag(name = "订单服务-订单控制器", description = "订单服务-订单控制器")
public class OrderController {
    @Resource
    private ISysOrderProcessService orderProcessService;

    /**
     * 从购物车创建订单
     *
     * @param request 创建订单请求
     * @return 创建订单响应
     */
    @DistributedLock(key = "'createOrder:' + #request.hashCode()", expire = 2)
    @Loggable(value = "从购物车创建订单")
    @Operation(summary = "从购物车创建订单", description = "用户从购物车触发创建订单，订单创建完毕本地事务提交后，发送MQ消息到order.created")
    @PostMapping("/create")
    public HttpResult<CreateOrderResponse> createOrder(@Validated @RequestBody CreateOrderRequest request) {
        log.info("创建订单请求: {}", request);
        CreateOrderResponse response = orderProcessService.createOrderFromCart(request);
        return HttpResult.ok(response);
    }

    /**
     * 查询订单列表
     *
     * @param request 查询请求
     * @return 订单列表响应
     */
    @Cacheable(value = "orderList",
               key = "'orderList:' + #request.userId + ':' + #request.pageNo + ':' + #request.pageSize + ':' + (#request.orderId != null ? #request.orderId : 'all')",
               unless = "#result == null || #result.data == null || #result.data.records.isEmpty()")
    @Loggable(value = "查询订单列表")
    @Operation(summary = "查询订单列表", description = "分页查询订单列表，支持订单号搜索，展示父订单包含子订单的聚合信息")
    @PostMapping("/list")
    public HttpResult<IPage<OrderListResponse.ParentOrderResponse>> queryOrderList(@Validated @RequestBody OrderListQueryRequest request) {
        log.info("查询订单列表请求: {}", request);
        IPage<OrderListResponse.ParentOrderResponse> response = orderProcessService.queryOrderList(request);
        return HttpResult.ok(response);
    }
}
