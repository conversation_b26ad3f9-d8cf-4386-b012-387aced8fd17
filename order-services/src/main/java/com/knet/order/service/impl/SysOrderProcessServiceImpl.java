package com.knet.order.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.knet.common.exception.ServiceException;
import com.knet.common.utils.PriceFormatUtil;
import com.knet.common.utils.RedisCacheUtil;
import com.knet.order.mapper.SysOrderGroupMapper;
import com.knet.order.model.dto.OrderItemDataDto;
import com.knet.order.model.dto.SubOrderDataDto;
import com.knet.order.model.dto.SubOrderSummaryDto;
import com.knet.order.model.dto.req.CreateOrderRequest;
import com.knet.order.model.dto.req.OrderListQueryRequest;
import com.knet.order.model.dto.rsp.CreateOrderResponse;
import com.knet.order.model.dto.rsp.OrderListResponse;
import com.knet.order.model.entity.SysOrder;
import com.knet.order.model.entity.SysOrderGroup;
import com.knet.order.model.entity.SysOrderItem;
import com.knet.order.service.ISysOrderGroupService;
import com.knet.order.service.ISysOrderItemService;
import com.knet.order.service.ISysOrderProcessService;
import com.knet.order.service.ISysOrderService;
import com.knet.order.system.event.OrderCreatedEvent;
import com.knet.order.system.event.OrderFailedEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.CacheManager;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/4 14:17
 * @description: 订单聚合服务接口实现
 */
@Slf4j
@Service
public class SysOrderProcessServiceImpl implements ISysOrderProcessService {
    @Resource
    private ApplicationEventPublisher eventPublisher;
    @Resource
    private ISysOrderService orderService;
    @Resource
    private ISysOrderItemService orderItemService;
    @Resource
    private ISysOrderGroupService orderGroupService;
    @Resource
    private SysOrderGroupMapper orderGroupMapper;
    @Resource
    private CacheManager cacheManager;
    @Resource
    private RedisCacheUtil redisCacheUtil;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public CreateOrderResponse createOrderFromCart(CreateOrderRequest request) {
        log.info("开始创建子母订单, userId: {}, items: {}", request.getUserId(), request.getItems().size());
        SysOrderGroup orderGroup = null;
        List<SubOrderDataDto> subOrderDataList = null;
        try {
            // 1. 验证请求数据并按商品分组
            Map<String, List<OrderItemDataDto>> productGroupMap = validateAndGroupOrderItemsFromRequest(request);
            if (productGroupMap.isEmpty()) {
                throw new ServiceException("订单商品数据为空");
            }
            // 2. 创建母订单记录
            orderGroup = orderGroupService.createOrderGroup(request.getUserId(), productGroupMap, request.getAddressId());
            // 3. 为每个商品创建子订单和明细
            subOrderDataList = createSubOrdersAndItems(orderGroup.getOrderId(), request.getUserId(), productGroupMap);
            // 4. 发送订单创建事件
            OrderCreatedEvent event = new OrderCreatedEvent(this, orderGroup);
            eventPublisher.publishEvent(event);
        } catch (Exception e) {
            log.error("订单创建失败: {}", e.getMessage(), e);
            OrderFailedEvent event = new OrderFailedEvent(this, request);
            //5. 发送订单创建失败事件
            pushOrderCreateFailedEvent(event);
            throw new ServiceException("订单创建失败: " + e.getMessage());
        }
        // 5. 清除用户订单列表缓存
        clearUserOrderListCache(request.getUserId());
        // 6. 构建响应
        CreateOrderResponse response = CreateOrderResponse.buildCreateOrderResponse(orderGroup, subOrderDataList);
        log.info("子母订单创建成功, parentOrderId: {}, subOrders: {}, totalAmount: {}",
                orderGroup.getOrderId(), subOrderDataList.size(), orderGroup.getTotalAmount());
        return response;
    }

    /**
     * 清除用户订单列表缓存
     * 使用RedisCacheUtil的deleteByPattern方法进行模糊匹配删除
     */
    private void clearUserOrderListCache(Long userId) {
        try {
            // 构建缓存key模式，匹配该用户的所有订单列表缓存
            // 格式：order-service:orderList:orderList:{userId}:*
            String cacheKeyPattern = "order-service:orderList:orderList:" + userId + ":*";

            // 使用RedisCacheUtil的deleteByPattern方法进行模糊匹配删除
            redisCacheUtil.deleteByPattern(cacheKeyPattern);

            log.info("已清除用户订单列表缓存，用户ID: {}, 缓存key模式: {}", userId, cacheKeyPattern);
        } catch (Exception e) {
            log.warn("清除用户订单列表缓存失败，用户ID: {}, 错误: {}", userId, e.getMessage());
        }
    }

    /**
     * 发送订单创建失败事件
     *
     * @param orderFailedEvent 订单创建事件
     */
    @Async
    public void pushOrderCreateFailedEvent(OrderFailedEvent orderFailedEvent) {
        eventPublisher.publishEvent(orderFailedEvent);
    }

    /**
     * 验证请求数据并按商品分组（支持相同SKU相同尺码不同定价）
     */
    private Map<String, List<OrderItemDataDto>> validateAndGroupOrderItemsFromRequest(CreateOrderRequest request) {
        Map<String, List<OrderItemDataDto>> productGroupMap = new HashMap<>(12);
        for (CreateOrderRequest.OrderItemRequest requestItem : request.getItems()) {
            List<OrderItemDataDto> itemDataList = new ArrayList<>();
            // 处理每个尺码明细（支持相同尺码不同定价）
            for (CreateOrderRequest.SizeDetailRequest sizeDetailRequest : requestItem.getSizeDetails()) {
                // 数量验证
                if (sizeDetailRequest.getQuantity() <= 0) {
                    throw new ServiceException("商品 " + requestItem.getSku() + " 尺码 " + sizeDetailRequest.getSize() + " 数量必须大于0");
                }
                OrderItemDataDto orderItemData = createOrderItem(requestItem, sizeDetailRequest);
                itemDataList.add(orderItemData);
            }
            // 合并同一SKU的商品数据
            if (productGroupMap.containsKey(requestItem.getSku())) {
                productGroupMap.get(requestItem.getSku()).addAll(itemDataList);
            } else {
                productGroupMap.put(requestItem.getSku(), itemDataList);
            }
        }
        return productGroupMap;
    }

    /**
     * 创建订单商品数据
     *
     * @param requestItem       requestItem
     * @param sizeDetailRequest sizeDetailRequest
     * @return OrderItemData
     */
    private static OrderItemDataDto createOrderItem(CreateOrderRequest.OrderItemRequest requestItem, CreateOrderRequest.SizeDetailRequest sizeDetailRequest) {
        BigDecimal unitPrice;
        try {
            unitPrice = new BigDecimal(sizeDetailRequest.getUnitPrice());
            if (unitPrice.compareTo(BigDecimal.ZERO) <= 0) {
                throw new ServiceException("商品 " + requestItem.getSku() + " 尺码 " + sizeDetailRequest.getSize() + " 单价必须大于0");
            }
        } catch (NumberFormatException e) {
            throw new ServiceException("商品 " + requestItem.getSku() + " 尺码 " + sizeDetailRequest.getSize() + " 单价格式错误");
        }
        // 创建订单商品数据（每个尺码明细对应一条记录，支持相同尺码不同定价）
        return OrderItemDataDto.createOrderItemDataDto(requestItem, sizeDetailRequest, unitPrice);
    }

    /**
     * 为每个商品创建子订单和明细
     */
    private List<SubOrderDataDto> createSubOrdersAndItems(String parentOrderId, Long userId,
                                                          Map<String, List<OrderItemDataDto>> productGroupMap) {
        List<SubOrderDataDto> subOrderDataList = new ArrayList<>();
        for (Map.Entry<String, List<OrderItemDataDto>> entry : productGroupMap.entrySet()) {
            String sku = entry.getKey();
            List<OrderItemDataDto> itemDataList = entry.getValue();
            // 创建子订单
            SysOrder subOrder = orderService.createSysOrder(parentOrderId, userId, sku, itemDataList);
            // 创建订单明细
            List<SysOrderItem> orderItems = orderItemService.createOrderItems(subOrder.getOrderId(), itemDataList);
            // 构建子订单数据
            SubOrderDataDto subOrderData = new SubOrderDataDto(subOrder, orderItems);
            subOrderDataList.add(subOrderData);
        }
        return subOrderDataList;
    }

    @Override
    public IPage<OrderListResponse.ParentOrderResponse> queryOrderList(OrderListQueryRequest request) {
        log.info("查询订单列表: {}", request);
        // 1. 分页查询父订单信息
        Page<OrderListResponse.ParentOrderResponse> page = new Page<>(request.getPageNo(), request.getPageSize());
        IPage<OrderListResponse.ParentOrderResponse> parentOrderPage = orderGroupMapper.queryOrderList(page, request);
        // 2. 如果没有查询到父订单，直接返回
        if (CollUtil.isEmpty(parentOrderPage.getRecords())) {
            return parentOrderPage;
        }
        // 3. 获取所有父订单ID
        List<String> parentOrderIds = parentOrderPage.getRecords()
                .stream()
                .map(OrderListResponse.ParentOrderResponse::getParentOrderId)
                .toList();
        // 4. 查询子订单汇总信息
        List<SubOrderSummaryDto> subOrderSummaryDos = orderGroupMapper.querySubOrderSummary(parentOrderIds);
        // 5. 转换DTO并按父订单ID分组子订单汇总
        Map<String, List<OrderListResponse.SubOrderSummaryResponse>> subOrderMap = subOrderSummaryDos
                .stream()
                .map(this::convertToSubOrderSummaryResponse)
                .collect(Collectors.groupingBy(OrderListResponse.SubOrderSummaryResponse::getParentOrderId));
        // 6. 为每个父订单设置子订单汇总信息并格式化价格
        parentOrderPage.getRecords().forEach(parentOrder -> {
            // 格式化父订单总金额
            if (parentOrder.getTotalAmount() != null) {
                parentOrder.setTotalAmount(PriceFormatUtil.formatPrice(parentOrder.getTotalAmount()));
            }
            // 设置子订单汇总信息
            List<OrderListResponse.SubOrderSummaryResponse> subOrders = subOrderMap.get(parentOrder.getParentOrderId());
            if (CollUtil.isNotEmpty(subOrders)) {
                parentOrder.setSubOrders(subOrders);
            } else {
                parentOrder.setSubOrders(new ArrayList<>());
            }
        });
        log.info("查询订单列表完成，共查询到 {} 条父订单", parentOrderPage.getRecords().size());
        return parentOrderPage;
    }

    /**
     * 转换SubOrderSummaryDto为SubOrderSummaryResponse
     */
    private OrderListResponse.SubOrderSummaryResponse convertToSubOrderSummaryResponse(SubOrderSummaryDto dto) {
        return OrderListResponse.SubOrderSummaryResponse.builder()
                .parentOrderId(dto.getParentOrderId())
                .sku(dto.getSku())
                .productName(dto.getProductName())
                .imageUrl(dto.getImageUrl())
                .totalQuantity(dto.getTotalQuantity())
                .avgPrice(dto.getAvgPrice() != null ? PriceFormatUtil.formatPrice(dto.getAvgPrice()) : "0.00")
                .totalPrice(dto.getTotalPrice() != null ? PriceFormatUtil.formatPrice(dto.getTotalPrice()) : "0.00")
                .status(dto.getStatus())
                .build();
    }
}
